function calculateOvertimeForConsole() {
    /**
 * 加班时间计算器
 * 根据打卡数据计算加班时长
 */

    /**
     * 计算工作日加班时间
     * @param {Array} punchTimes - 打卡时间数组
     * @returns {Object} 加班信息
     */
    const calculateWeekdayOvertime = (punchTimes) => {
        // 如果第一次打卡时间大于12点，不统计加班
        const firstPunch = parseTime(punchTimes[0]);
        if (firstPunch.hours >= 12) {
            return { hours: 0, minutes: 0, reason: '第一次打卡时间大于12点' };
        }

        // 如果只有一次打卡且在19点前，不统计加班
        if (punchTimes.length === 1) {
            if (firstPunch.hours < 19) {
                return { hours: 0, minutes: 0, reason: '无下班卡且19点前无打卡记录' };
            }
        }

        // 找到最后一次打卡时间
        const lastPunch = parseTime(punchTimes[punchTimes.length - 1]);

        // 如果最后一次打卡在19点前，不统计加班
        if (lastPunch.hours < 19) {
            return { hours: 0, minutes: 0, reason: '下班卡未超过19点' };
        }

        // 计算标准下班时间（考虑弹性打卡）
        const standardEndTime = calculateStandardEndTime(firstPunch);

        // 计算加班时间（从19点开始算加班）
        const overtimeStartTime = { hours: 19, minutes: 0 };
        const overtimeMinutes = calculateTimeDifference(overtimeStartTime, lastPunch);

        // 减去早上弹性时间（如果晚到了）
        const flexMinutes = calculateFlexTime(firstPunch);
        const finalOvertimeMinutes = Math.max(0, overtimeMinutes - flexMinutes);

        return {
            hours: Math.floor(finalOvertimeMinutes / 60),
            minutes: finalOvertimeMinutes % 60,
            reason: finalOvertimeMinutes > 0 ? '工作日加班' : '无加班'
        };
    }

    /**
     * 计算周末加班时间
     * @param {Array} punchTimes - 打卡时间数组
     * @returns {Object} 加班信息
     */
    const calculateWeekendOvertime = (punchTimes) => {
        if (punchTimes.length < 2) {
            return { hours: 0, minutes: 0, reason: '周末打卡记录不完整' };
        }

        // 按时间排序
        const sortedTimes = punchTimes.map(parseTime).sort((a, b) => {
            return (a.hours * 60 + a.minutes) - (b.hours * 60 + b.minutes);
        });

        const firstPunch = sortedTimes[0];
        const lastPunch = sortedTimes[sortedTimes.length - 1];

        // 计算总工作时间
        let totalMinutes = calculateTimeDifference(firstPunch, lastPunch);

        // 如果跨越午休时间（12:00-13:00），减去午休时间
        if (firstPunch.hours < 12 && lastPunch.hours >= 13) {
            totalMinutes -= 60; // 减去1小时午休
        } else if (firstPunch.hours < 13 && lastPunch.hours >= 13 &&
            (firstPunch.hours > 12 || (firstPunch.hours === 12 && firstPunch.minutes > 0))) {
            // 如果开始时间在12点后但13点前，只减去部分午休时间
            const lunchStartMinutes = Math.max(0, 60 - firstPunch.minutes);
            totalMinutes -= lunchStartMinutes;
        }

        return {
            hours: Math.floor(totalMinutes / 60),
            minutes: totalMinutes % 60,
            reason: totalMinutes > 0 ? '周末加班' : '无加班'
        };
    }

    /**
     * 解析时间字符串
     * @param {string} timeStr - 时间字符串 "HH:MM:SS"
     * @returns {Object} {hours, minutes, seconds}
     */
    const parseTime = (timeStr) => {
        const parts = timeStr.split(':');
        return {
            hours: parseInt(parts[0], 10),
            minutes: parseInt(parts[1], 10),
            seconds: parseInt(parts[2], 10)
        };
    }

    /**
     * 计算标准下班时间（考虑弹性打卡）
     * @param {Object} firstPunch - 第一次打卡时间
     * @returns {Object} 标准下班时间
     */
    const calculateStandardEndTime = (firstPunch) => {
        const standardStartHour = 8;
        const standardStartMinute = 30;
        const standardEndHour = 18;
        const standardEndMinute = 0;

        // 如果在标准时间前打卡，下班时间为18:00
        if (firstPunch.hours < standardStartHour ||
            (firstPunch.hours === standardStartHour && firstPunch.minutes <= standardStartMinute)) {
            return { hours: standardEndHour, minutes: standardEndMinute };
        }

        // 如果晚到，下班时间相应延后
        const lateMinutes = (firstPunch.hours - standardStartHour) * 60 +
            (firstPunch.minutes - standardStartMinute);
        const endTotalMinutes = standardEndHour * 60 + standardEndMinute + lateMinutes;

        return {
            hours: Math.floor(endTotalMinutes / 60),
            minutes: endTotalMinutes % 60
        };
    }

    /**
     * 计算弹性时间（早上晚到的时间）
     * @param {Object} firstPunch - 第一次打卡时间
     * @returns {number} 弹性时间（分钟）
     */
    const calculateFlexTime = (firstPunch) => {
        const standardStartHour = 8;
        const standardStartMinute = 30;

        if (firstPunch.hours < standardStartHour ||
            (firstPunch.hours === standardStartHour && firstPunch.minutes <= standardStartMinute)) {
            return 0;
        }

        return (firstPunch.hours - standardStartHour) * 60 +
            (firstPunch.minutes - standardStartMinute);
    }

    /**
     * 计算两个时间之间的分钟差
     * @param {Object} startTime - 开始时间
     * @param {Object} endTime - 结束时间
     * @returns {number} 分钟差
     */
    const calculateTimeDifference = (startTime, endTime) => {
        const startMinutes = startTime.hours * 60 + startTime.minutes;
        const endMinutes = endTime.hours * 60 + endTime.minutes;
        return Math.max(0, endMinutes - startMinutes);
    }

    /**
     * 计算单日加班时间
     * @param {Object} record - 单日打卡记录
     * @returns {Object} 加班信息 {hours, minutes, reason}
     */
    const calculateDailyOvertime = (record) => {
        const { date, dayOfWeek, punchTimes } = record;

        // 过滤掉空的打卡时间
        const validPunchTimes = punchTimes.filter(time => time && time.trim() !== '');

        // 如果没有打卡记录，返回0
        if (validPunchTimes.length === 0) {
            return { hours: 0, minutes: 0, reason: '无打卡记录' };
        }

        // 判断是否为周末
        const isWeekend = dayOfWeek === '六' || dayOfWeek === '日';

        if (isWeekend) {
            return calculateWeekendOvertime(validPunchTimes);
        } else {
            return calculateWeekdayOvertime(validPunchTimes);
        }
    }
    /**
     * 计算加班时间
     * @param {Array} attendanceData - 打卡数据数组
     * @returns {Array} 包含加班信息的数据数组
     */
    const calculateOvertime = (attendanceData) => {
        const results = [];

        attendanceData.forEach(record => {
            const overtimeInfo = calculateDailyOvertime(record);
            results.push({
                ...record,
                overtimeHours: overtimeInfo.hours,
                overtimeMinutes: overtimeInfo.minutes,
                overtimeReason: overtimeInfo.reason
            });
        });

        return results;
    }
    /**
     * 从HTML表格中提取打卡数据
     * @returns {Array} 打卡数据数组
     */
    const extractAttendanceDataFromTable = () => {
        const table = document.querySelector('table');
        if (!table) {
            throw new Error('找不到表格');
        }

        const rows = table.querySelectorAll('tbody tr');
        const data = [];

        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length >= 8) {
                const punchTimes = [];
                for (let i = 3; i <= 8; i++) {
                    const timeText = cells[i].textContent.trim();
                    if (timeText) {
                        punchTimes.push(timeText);
                    }
                }

                data.push({
                    序号: cells[0].textContent.trim(),
                    date: cells[1].textContent.trim(),
                    dayOfWeek: cells[2].textContent.trim(),
                    punchTimes: punchTimes
                });
            }
        });

        return data;
    }
    try {
        console.log('🚀 开始计算加班时间...');

        // 从表格中提取数据
        const attendanceData = extractAttendanceDataFromTable();
        console.log('📊 提取到打卡数据:', attendanceData.length, '条记录');

        // 计算加班时间
        const overtimeResults = calculateOvertime(attendanceData);

        // 移除之前的结果（如果存在）
        const existingResult = document.getElementById('console-overtime-result');
        if (existingResult) {
            existingResult.remove();
        }

        // 创建结果容器
        const resultContainer = document.createElement('div');
        resultContainer.id = 'console-overtime-result';
        resultContainer.style.cssText = `
        margin: 30px auto;
        padding: 20px;
        max-width: 800px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        color: white;
        font-family: 'Microsoft YaHei', Arial, sans-serif;
        position: relative;
        overflow: hidden;
    `;

        // 添加背景装饰
        resultContainer.innerHTML = `
        <div style="position: absolute; top: -50%; right: -50%; width: 100%; height: 100%; background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%); pointer-events: none;"></div>
        <div style="position: relative; z-index: 1;">
            <h2 style="margin: 0 0 20px 0; font-size: 24px; text-align: center; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">
                ⏰ 加班时间统计结果
            </h2>
            <div id="overtime-details-console" style="margin-bottom: 20px;"></div>
            <div id="total-overtime-console" style="font-size: 20px; font-weight: bold; text-align: center; padding: 15px; background: rgba(255,255,255,0.2); border-radius: 8px; text-shadow: 0 1px 2px rgba(0,0,0,0.3);"></div>
            <div style="text-align: center; margin-top: 15px; font-size: 12px; opacity: 0.8;">
                💡 由控制台函数 calculateOvertimeForConsole() 生成
            </div>
        </div>
    `;

        const detailsDiv = resultContainer.querySelector('#overtime-details-console');
        const totalDiv = resultContainer.querySelector('#total-overtime-console');

        // 显示每日加班详情
        let hasOvertime = false;
        let consoleLog = '\n📈 加班详情:\n';

        overtimeResults.forEach(record => {
            if (record.overtimeHours > 0 || record.overtimeMinutes > 0) {
                hasOvertime = true;
                const detailDiv = document.createElement('div');
                detailDiv.style.cssText = `
                margin-bottom: 8px;
                padding: 10px;
                background: rgba(255,255,255,0.15);
                border-radius: 6px;
                border-left: 4px solid #ffd700;
            `;
                detailDiv.innerHTML = `
                <strong>${record.date} (${record.dayOfWeek}):</strong>
                <span style="color: #ffd700; font-weight: bold;">${record.overtimeHours}小时${record.overtimeMinutes}分钟</span>
                <span style="opacity: 0.8; font-size: 14px;"> - ${record.overtimeReason}</span>
            `;
                detailsDiv.appendChild(detailDiv);

                consoleLog += `  ${record.date} (${record.dayOfWeek}): ${record.overtimeHours}小时${record.overtimeMinutes}分钟 - ${record.overtimeReason}\n`;
            }
        });

        if (!hasOvertime) {
            detailsDiv.innerHTML = `
            <div style="text-align: center; padding: 20px; opacity: 0.8; font-style: italic;">
                😴 本期间无加班记录
            </div>
        `;
            consoleLog += '  本期间无加班记录\n';
        }

        // 计算总加班时间
        const totalOvertimeMinutes = overtimeResults.reduce((total, record) => {
            return total + record.overtimeHours * 60 + record.overtimeMinutes;
        }, 0);

        const totalHours = Math.floor(totalOvertimeMinutes / 60);
        const totalMinutes = totalOvertimeMinutes % 60;

        totalDiv.innerHTML = `🎯 总加班时间: ${totalHours}小时${totalMinutes}分钟`;

        // 插入到页面底部
        document.body.appendChild(resultContainer);

        // 滚动到结果位置
        resultContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // 控制台输出
        console.log(consoleLog);
        console.log(`🎯 总加班时间: ${totalHours}小时${totalMinutes}分钟`);
        console.log('✅ 结果已插入到页面底部');

        // 返回结果数据
        return {
            success: true,
            totalHours,
            totalMinutes,
            details: overtimeResults.filter(r => r.overtimeHours > 0 || r.overtimeMinutes > 0),
            allData: overtimeResults
        };

    } catch (error) {
        console.error('❌ 计算加班时间时出错：', error);

        // 显示错误信息到页面
        const errorContainer = document.createElement('div');
        errorContainer.style.cssText = `
        margin: 30px auto;
        padding: 20px;
        max-width: 600px;
        background: #dc3545;
        border-radius: 8px;
        color: white;
        text-align: center;
        font-family: 'Microsoft YaHei', Arial, sans-serif;
    `;
        errorContainer.innerHTML = `
        <h3>❌ 计算出错</h3>
        <p>${error.message}</p>
        <small>请检查页面是否包含正确的打卡表格</small>
    `;
        document.body.appendChild(errorContainer);

        return { success: false, error: error.message };
    }
}