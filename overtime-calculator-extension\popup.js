document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculateBtn');
    const clearBtn = document.getElementById('clearBtn');
    const status = document.getElementById('status');
    
    // 显示状态信息
    function showStatus(message, type = 'info') {
        status.textContent = message;
        status.className = `status ${type}`;
        status.style.display = 'block';
        
        // 3秒后自动隐藏
        setTimeout(() => {
            status.style.display = 'none';
        }, 3000);
    }
    
    // 计算加班时间
    calculateBtn.addEventListener('click', async function() {
        try {
            showStatus('正在计算加班时间...', 'info');
            calculateBtn.disabled = true; // 防止重复点击

            // 获取当前活动标签页
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            // 先检查页面是否有gridtable元素（包括iframe中的）
            const checkResults = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                function: () => {
                    // 查找gridtable元素的辅助函数
                    const findGridTable = () => {
                        // 先在主文档中查找
                        let gridTable = document.getElementById('gridtable');
                        if (gridTable) {
                            return gridTable;
                        }

                        // 在iframe中查找
                        const iframes = document.querySelectorAll('iframe');
                        for (let iframe of iframes) {
                            try {
                                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                                gridTable = iframeDoc.getElementById('gridtable');
                                if (gridTable) {
                                    return gridTable;
                                }
                            } catch (e) {
                                // 跨域iframe无法访问，跳过
                                continue;
                            }
                        }
                        return null;
                    };

                    const gridTable = findGridTable();
                    return {
                        hasGridTable: !!gridTable,
                        hasTable: gridTable ? !!gridTable.querySelector('table') : false
                    };
                }
            });

            const pageCheck = checkResults[0].result;

            if (!pageCheck.hasGridTable) {
                showStatus('❌ 页面中找不到id为gridtable的元素', 'error');
                return;
            }

            if (!pageCheck.hasTable) {
                showStatus('❌ gridtable元素下找不到表格', 'error');
                return;
            }

            // 注入计算脚本
            const results = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                function: calculateOvertimeInPage
            });

            const result = results[0].result;

            if (result && result.success) {
                showStatus(`✅ 计算完成！总加班: ${result.totalHours}小时${result.totalMinutes}分钟`, 'success');
            } else {
                showStatus(`❌ ${result ? result.error : '计算失败'}`, 'error');
            }

        } catch (error) {
            console.error('执行脚本时出错:', error);
            showStatus('❌ 执行失败，请刷新页面后重试', 'error');
        } finally {
            calculateBtn.disabled = false; // 重新启用按钮
        }
    });
    
    // 清除结果
    clearBtn.addEventListener('click', async function() {
        try {
            showStatus('正在清除结果...', 'info');
            clearBtn.disabled = true; // 防止重复点击

            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                function: clearOvertimeResults
            });

            showStatus('🗑️ 结果已清除', 'success');

        } catch (error) {
            console.error('清除结果时出错:', error);
            showStatus('❌ 清除失败', 'error');
        } finally {
            clearBtn.disabled = false; // 重新启用按钮
        }
    });
});

// 在页面中执行的计算函数
function calculateOvertimeInPage() {
    /**
     * 查找gridtable元素（支持iframe）
     * @returns {Object} {element: HTMLElement, document: Document}
     */
    const findGridTable = () => {
        // 先在主文档中查找
        let gridTable = document.getElementById('gridtable');
        if (gridTable) {
            return { element: gridTable, document: document };
        }

        // 在iframe中查找
        const iframes = document.querySelectorAll('iframe');
        for (let iframe of iframes) {
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                gridTable = iframeDoc.getElementById('gridtable');
                if (gridTable) {
                    return { element: gridTable, document: iframeDoc };
                }
            } catch (e) {
                // 跨域iframe无法访问，跳过
                continue;
            }
        }

        return null;
    };
    /**
     * 计算工作日加班时间
     * @param {Array} punchTimes - 打卡时间数组
     * @returns {Object} 加班信息
     */
    const calculateWeekdayOvertime = (punchTimes) => {
        // 如果第一次打卡时间大于12点，不统计加班
        const firstPunch = parseTime(punchTimes[0]);
        if (firstPunch.hours >= 12) {
            return { hours: 0, minutes: 0, reason: '第一次打卡时间大于12点' };
        }

        // 如果只有一次打卡且在18点30分前，不统计加班
        if (punchTimes.length === 1) {
            if (firstPunch.hours < 18 || (firstPunch.hours === 18 && firstPunch.minutes < 30)) {
                return { hours: 0, minutes: 0, reason: '无下班卡且18点30分前无打卡记录' };
            }
        }

        // 找到最后一次打卡时间
        const lastPunch = parseTime(punchTimes[punchTimes.length - 1]);

        // 如果最后一次打卡在18点30分前，不统计加班
        if (lastPunch.hours < 18 || (lastPunch.hours === 18 && lastPunch.minutes < 30)) {
            return { hours: 0, minutes: 0, reason: '下班卡未超过18点30分' };
        }

        // 计算标准下班时间（考虑弹性打卡）
        const standardEndTime = calculateStandardEndTime(firstPunch);

        // 计算加班时间（从18点30分开始算加班）
        const overtimeStartTime = { hours: 18, minutes: 30 };
        const overtimeMinutes = calculateTimeDifference(overtimeStartTime, lastPunch);

        // 减去早上弹性时间（如果晚到了）
        const flexMinutes = calculateFlexTime(firstPunch);
        const finalOvertimeMinutes = Math.max(0, overtimeMinutes - flexMinutes);

        return {
            hours: Math.floor(finalOvertimeMinutes / 60),
            minutes: finalOvertimeMinutes % 60,
            reason: finalOvertimeMinutes > 0 ? '工作日加班' : '无加班'
        };
    }

    /**
     * 计算周末加班时间
     * @param {Array} punchTimes - 打卡时间数组
     * @returns {Object} 加班信息
     */
    const calculateWeekendOvertime = (punchTimes) => {
        if (punchTimes.length < 2) {
            return { hours: 0, minutes: 0, reason: '周末打卡记录不完整' };
        }

        // 按时间排序
        const sortedTimes = punchTimes.map(parseTime).sort((a, b) => {
            return (a.hours * 60 + a.minutes) - (b.hours * 60 + b.minutes);
        });

        const firstPunch = sortedTimes[0];
        const lastPunch = sortedTimes[sortedTimes.length - 1];

        // 计算总工作时间
        let totalMinutes = calculateTimeDifference(firstPunch, lastPunch);

        // 如果跨越午休时间（12:00-13:00），减去午休时间
        if (firstPunch.hours < 12 && lastPunch.hours >= 13) {
            totalMinutes -= 60; // 减去1小时午休
        } else if (firstPunch.hours < 13 && lastPunch.hours >= 13 &&
            (firstPunch.hours > 12 || (firstPunch.hours === 12 && firstPunch.minutes > 0))) {
            // 如果开始时间在12点后但13点前，只减去部分午休时间
            const lunchStartMinutes = Math.max(0, 60 - firstPunch.minutes);
            totalMinutes -= lunchStartMinutes;
        }

        return {
            hours: Math.floor(totalMinutes / 60),
            minutes: totalMinutes % 60,
            reason: totalMinutes > 0 ? '周末加班' : '无加班'
        };
    }

    /**
     * 解析时间字符串
     * @param {string} timeStr - 时间字符串 "HH:MM:SS"
     * @returns {Object} {hours, minutes, seconds}
     */
    const parseTime = (timeStr) => {
        const parts = timeStr.split(':');
        return {
            hours: parseInt(parts[0], 10),
            minutes: parseInt(parts[1], 10),
            seconds: parseInt(parts[2], 10)
        };
    }

    /**
     * 计算标准下班时间（考虑弹性打卡）
     * @param {Object} firstPunch - 第一次打卡时间
     * @returns {Object} 标准下班时间
     */
    const calculateStandardEndTime = (firstPunch) => {
        const standardStartHour = 8;
        const standardStartMinute = 30;
        const standardEndHour = 18;
        const standardEndMinute = 0;

        // 如果在标准时间前打卡，下班时间为18:00
        if (firstPunch.hours < standardStartHour ||
            (firstPunch.hours === standardStartHour && firstPunch.minutes <= standardStartMinute)) {
            return { hours: standardEndHour, minutes: standardEndMinute };
        }

        // 如果晚到，下班时间相应延后
        const lateMinutes = (firstPunch.hours - standardStartHour) * 60 +
            (firstPunch.minutes - standardStartMinute);
        const endTotalMinutes = standardEndHour * 60 + standardEndMinute + lateMinutes;

        return {
            hours: Math.floor(endTotalMinutes / 60),
            minutes: endTotalMinutes % 60
        };
    }

    /**
     * 计算弹性时间（早上晚到的时间）
     * @param {Object} firstPunch - 第一次打卡时间
     * @returns {number} 弹性时间（分钟）
     */
    const calculateFlexTime = (firstPunch) => {
        const standardStartHour = 8;
        const standardStartMinute = 30;

        if (firstPunch.hours < standardStartHour ||
            (firstPunch.hours === standardStartHour && firstPunch.minutes <= standardStartMinute)) {
            return 0;
        }

        return (firstPunch.hours - standardStartHour) * 60 +
            (firstPunch.minutes - standardStartMinute);
    }

    /**
     * 计算两个时间之间的分钟差
     * @param {Object} startTime - 开始时间
     * @param {Object} endTime - 结束时间
     * @returns {number} 分钟差
     */
    const calculateTimeDifference = (startTime, endTime) => {
        const startMinutes = startTime.hours * 60 + startTime.minutes;
        const endMinutes = endTime.hours * 60 + endTime.minutes;
        return Math.max(0, endMinutes - startMinutes);
    }

    /**
     * 计算单日加班时间
     * @param {Object} record - 单日打卡记录
     * @returns {Object} 加班信息 {hours, minutes, reason}
     */
    const calculateDailyOvertime = (record) => {
        const { date, dayOfWeek, punchTimes } = record;

        // 过滤掉空的打卡时间
        const validPunchTimes = punchTimes.filter(time => time && time.trim() !== '');

        // 如果没有打卡记录，返回0
        if (validPunchTimes.length === 0) {
            return { hours: 0, minutes: 0, reason: '无打卡记录' };
        }

        // 判断是否为周末
        const isWeekend = dayOfWeek === '六' || dayOfWeek === '日';

        if (isWeekend) {
            return calculateWeekendOvertime(validPunchTimes);
        } else {
            return calculateWeekdayOvertime(validPunchTimes);
        }
    }

    /**
     * 计算加班时间
     * @param {Array} attendanceData - 打卡数据数组
     * @returns {Array} 包含加班信息的数据数组
     */
    const calculateOvertime = (attendanceData) => {
        const results = [];

        attendanceData.forEach(record => {
            const overtimeInfo = calculateDailyOvertime(record);
            results.push({
                ...record,
                overtimeHours: overtimeInfo.hours,
                overtimeMinutes: overtimeInfo.minutes,
                overtimeReason: overtimeInfo.reason
            });
        });

        return results;
    }
    
    /**
     * 从HTML表格中提取打卡数据（支持iframe）
     * @returns {Array} 打卡数据数组
     */
    const extractAttendanceDataFromTable = () => {
        const gridTableInfo = findGridTable();
        if (!gridTableInfo) {
            throw new Error('找不到id为gridtable的元素（已检查主文档和iframe）');
        }

        const { element: gridTable, document: targetDoc } = gridTableInfo;
        const table = gridTable.querySelector('table');
        if (!table) {
            throw new Error('在gridtable元素及其子元素下找不到表格');
        }

        const rows = table.querySelectorAll('tbody tr');
        const data = [];

        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length >= 8) {
                const punchTimes = [];
                for (let i = 3; i <= 8; i++) {
                    const timeText = cells[i].textContent.trim();
                    if (timeText) {
                        punchTimes.push(timeText);
                    }
                }

                data.push({
                    序号: cells[0].textContent.trim(),
                    date: cells[1].textContent.trim(),
                    dayOfWeek: cells[2].textContent.trim(),
                    punchTimes: punchTimes
                });
            }
        });

        return data;
    }
    
    try {
        // 快速检查是否有必要的元素（支持iframe）
        const gridTableInfo = findGridTable();
        if (!gridTableInfo) {
            return { success: false, error: '找不到id为gridtable的元素（已检查主文档和iframe）' };
        }

        const { element: gridTable } = gridTableInfo;
        const table = gridTable.querySelector('table');
        if (!table) {
            return { success: false, error: '在gridtable元素下找不到表格' };
        }

        const attendanceData = extractAttendanceDataFromTable();
        if (!attendanceData || attendanceData.length === 0) {
            return { success: false, error: '未找到有效的打卡数据' };
        }

        const overtimeResults = calculateOvertime(attendanceData);

        // 移除之前的结果
        const existingResult = document.getElementById('overtime-extension-result');
        if (existingResult) {
            existingResult.remove();
        }
        
        // 创建结果显示
        const resultContainer = document.createElement('div');
        resultContainer.id = 'overtime-extension-result';
        resultContainer.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 400px;
            max-height: 500px;
            overflow-y: auto;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            color: white;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            z-index: 10000;
            padding: 20px;
        `;
        
        // 计算总加班时间
        const totalOvertimeMinutes = overtimeResults.reduce((total, record) => {
            return total + record.overtimeHours * 60 + record.overtimeMinutes;
        }, 0);
        
        const totalHours = Math.floor(totalOvertimeMinutes / 60);
        const totalMinutes = totalOvertimeMinutes % 60;
        
        // 生成HTML内容
        let detailsHTML = '';
        let hasOvertime = false;
        
        overtimeResults.forEach(record => {
            if (record.overtimeHours > 0 || record.overtimeMinutes > 0) {
                hasOvertime = true;
                detailsHTML += `
                    <div style="margin-bottom: 8px; padding: 8px; background: rgba(255,255,255,0.15); border-radius: 6px; border-left: 4px solid #ffd700;">
                        <strong>${record.date} (${record.dayOfWeek}):</strong> 
                        <span style="color: #ffd700; font-weight: bold;">${record.overtimeHours}小时${record.overtimeMinutes}分钟</span>
                        <span style="opacity: 0.8; font-size: 12px;"> - ${record.overtimeReason}</span>
                    </div>
                `;
            }
        });
        
        if (!hasOvertime) {
            detailsHTML = '<div style="text-align: center; padding: 20px; opacity: 0.8;">😴 本期间无加班记录</div>';
        }
        
        resultContainer.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <h3 style="margin: 0; font-size: 18px;">⏰ 加班统计</h3>
                <button onclick="this.parentElement.parentElement.remove()" style="background: rgba(255,255,255,0.2); border: none; color: white; border-radius: 50%; width: 24px; height: 24px; cursor: pointer; font-size: 14px;">×</button>
            </div>
            <div style="margin-bottom: 15px; max-height: 300px; overflow-y: auto;">
                ${detailsHTML}
            </div>
            <div style="text-align: center; font-size: 16px; font-weight: bold; padding: 12px; background: rgba(255,255,255,0.2); border-radius: 8px;">
                🎯 总加班时间: ${totalHours}小时${totalMinutes}分钟
            </div>
            <div style="text-align: center; margin-top: 10px; font-size: 11px; opacity: 0.7;">
                由加班计算器插件生成
            </div>
        `;
        
        document.body.appendChild(resultContainer);
        
        return {
            success: true,
            totalHours,
            totalMinutes,
            details: overtimeResults.filter(r => r.overtimeHours > 0 || r.overtimeMinutes > 0)
        };
        
    } catch (error) {
        return { success: false, error: error.message };
    }
}

// 清除结果的函数
function clearOvertimeResults() {
    const existingResult = document.getElementById('overtime-extension-result');
    if (existingResult) {
        existingResult.remove();
        return true;
    }
    return false;
}
