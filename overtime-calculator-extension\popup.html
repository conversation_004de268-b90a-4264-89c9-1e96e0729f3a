<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 320px;
            padding: 20px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 5px 0 0 0;
            font-size: 12px;
            opacity: 0.8;
        }
        
        .button {
            width: 100%;
            padding: 12px;
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            color: white;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .button:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            transform: translateY(-1px);
        }
        
        .button:active {
            transform: translateY(0);
        }

        .button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .button:disabled:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.3);
            transform: none;
        }
        
        .status {
            margin-top: 15px;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 6px;
            font-size: 12px;
            text-align: center;
            min-height: 20px;
            display: none;
        }
        
        .success {
            background: rgba(40, 167, 69, 0.3);
            border: 1px solid rgba(40, 167, 69, 0.5);
        }
        
        .error {
            background: rgba(220, 53, 69, 0.3);
            border: 1px solid rgba(220, 53, 69, 0.5);
        }
        
        .info {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-top: 15px;
            padding: 10px;
            border-radius: 6px;
            font-size: 11px;
            line-height: 1.4;
        }
        
        .info h4 {
            margin: 0 0 8px 0;
            font-size: 12px;
        }
        
        .info ul {
            margin: 5px 0;
            padding-left: 15px;
        }
        
        .info li {
            margin-bottom: 3px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>⏰ 加班时间计算器</h1>
        <p>智能识别打卡表格并计算加班时间</p>
    </div>
    
    <button id="calculateBtn" class="button">
        🚀 开始计算加班时间
    </button>
    
    <button id="clearBtn" class="button">
        🗑️ 清除页面结果
    </button>
    
    <div id="status" class="status"></div>
    
    <div class="info">
        <h4>📋 计算规则：</h4>
        <ul>
            <li><strong>工作日：</strong>8:30-18:00为标准工作时间</li>
            <li><strong>弹性打卡：</strong>支持半小时弹性</li>
            <li><strong>加班统计：</strong>18:30后开始计算加班</li>
            <li><strong>周末：</strong>全天算加班，自动扣除午休</li>
        </ul>
        
        <h4>🎯 使用说明：</h4>
        <ul>
            <li>在包含打卡表格的页面点击计算按钮</li>
            <li>结果会自动显示在页面底部</li>
            <li>支持多次计算，自动更新结果</li>
        </ul>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
