// Content script for overtime calculator extension
// This script runs on all pages and provides additional functionality

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'calculateOvertime') {
        try {
            const result = calculateOvertimeInCurrentPage();
            sendResponse({ success: true, data: result });
        } catch (error) {
            sendResponse({ success: false, error: error.message });
        }
    } else if (request.action === 'clearResults') {
        clearOvertimeResults();
        sendResponse({ success: true });
    }
    return true; // 保持消息通道开放
});

// 添加快捷键支持 - 带防抖功能
let isCalculating = false;
let lastKeyTime = 0;

document.addEventListener('keydown', function(event) {
    const now = Date.now();

    // 防抖：500ms内只能触发一次
    if (now - lastKeyTime < 500) {
        return;
    }

    // Ctrl+Shift+O 快速计算加班时间
    if (event.ctrlKey && event.shiftKey && event.key === 'O') {
        event.preventDefault();

        if (isCalculating) {
            console.log('⏳ 正在计算中，请稍候...');
            return;
        }

        lastKeyTime = now;
        isCalculating = true;

        try {
            calculateOvertimeInCurrentPage();
            console.log('🚀 快捷键触发：加班时间计算完成');
        } catch (error) {
            console.error('❌ 快捷键计算失败:', error);
        } finally {
            setTimeout(() => { isCalculating = false; }, 1000);
        }
    }

    // Ctrl+Shift+C 清除结果
    if (event.ctrlKey && event.shiftKey && event.key === 'C') {
        event.preventDefault();
        lastKeyTime = now;
        clearOvertimeResults();
        console.log('🗑️ 快捷键触发：结果已清除');
    }
});

// 页面加载完成后的初始化 - 延迟检查以提高性能
function initializeExtension() {
    // 使用setTimeout延迟检查，避免阻塞页面加载
    setTimeout(() => {
        const gridTable = document.getElementById('gridtable');
        if (gridTable && gridTable.querySelector('table')) {
            console.log('📊 检测到gridtable中的打卡表格，加班计算器插件已就绪');
            console.log('💡 使用方法：');
            console.log('  - 点击插件图标使用界面操作');
            console.log('  - 快捷键 Ctrl+Shift+O 快速计算');
            console.log('  - 快捷键 Ctrl+Shift+C 清除结果');
        }
    }, 1000); // 延迟1秒检查
}

// 使用多种方式确保初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeExtension);
} else {
    initializeExtension();
}

// 在当前页面计算加班时间的函数
function calculateOvertimeInCurrentPage() {
    try {
        // 检查是否存在gridtable元素
        const gridTable = document.getElementById('gridtable');
        if (!gridTable) {
            throw new Error('找不到id为gridtable的元素');
        }

        const table = gridTable.querySelector('table');
        if (!table) {
            throw new Error('在gridtable元素及其子元素下找不到表格');
        }

        console.log('📊 检测到打卡表格，开始计算加班时间...');

        // 发送消息给popup进行计算
        chrome.runtime.sendMessage({action: 'calculateOvertime'}, (response) => {
            if (response && response.success) {
                console.log('✅ 计算完成！总加班时间:', response.data.totalHours + '小时' + response.data.totalMinutes + '分钟');
            } else {
                console.error('❌ 计算失败:', response ? response.error : '未知错误');
            }
        });

        return { success: true, message: '计算请求已发送' };
    } catch (error) {
        console.error('❌ 计算加班时间失败:', error);
        throw error;
    }
}

// 清除结果的函数
function clearOvertimeResults() {
    const existingResult = document.getElementById('overtime-extension-result');
    if (existingResult) {
        existingResult.remove();
        return true;
    }
    
    // 同时清除可能存在的控制台版本结果
    const consoleResult = document.getElementById('console-overtime-result');
    if (consoleResult) {
        consoleResult.remove();
    }
    
    return false;
}

// 添加页面右键菜单提示（通过CSS实现）
const style = document.createElement('style');
style.textContent = `
    /* 为插件结果添加动画效果 */
    #overtime-extension-result {
        animation: slideInFromRight 0.3s ease-out;
    }
    
    @keyframes slideInFromRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    /* 滚动条样式优化 */
    #overtime-extension-result::-webkit-scrollbar {
        width: 6px;
    }
    
    #overtime-extension-result::-webkit-scrollbar-track {
        background: rgba(255,255,255,0.1);
        border-radius: 3px;
    }
    
    #overtime-extension-result::-webkit-scrollbar-thumb {
        background: rgba(255,255,255,0.3);
        border-radius: 3px;
    }
    
    #overtime-extension-result::-webkit-scrollbar-thumb:hover {
        background: rgba(255,255,255,0.5);
    }
`;
document.head.appendChild(style);
